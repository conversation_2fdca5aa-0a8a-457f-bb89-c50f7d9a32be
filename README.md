# 药房小程序

## 项目概述

base on unibest : https://feige996.github.io/unibest/

药房小程序是一个使用现代 Web 技术构建的综合应用。它利用 `uniapp`、`Vue3`、`TypeScript` 和 `Vite`，为 Web、小程序和 App 平台提供无缝的开发体验。

## 主要功能

- **状态管理**：使用 `Pinia` 进行状态管理，并支持状态持久化。
- **自定义组件**：包括 `fg-tabbar` 和 `fg-navbar` 等自定义组件，以增强用户界面和用户体验。
- **平台兼容性**：支持多平台，包括 H5、iOS、Android 和各种小程序。

## UI 库和插件

项目中使用了以下 UI 库和插件来增强开发体验和功能：

- **Wot Design Uni**: 使用 `wot-design-uni` 作为主要的 UI 组件库 （ https://wot-design-uni.netlify.app/ ）
- **UniApp**: 使用 `@dcloudio/vite-plugin-uni` 插件来支持 UniApp 的开发。
- **UnoCSS**: 使用 `unocss/vite` 插件来提供原子化 CSS 支持，简化样式管理。
- **自动导入**: 使用 `unplugin-auto-import/vite` 插件自动导入 Vue 和 UniApp 的 API，减少手动导入的繁琐。
- **组件自动注册**: 使用 `@uni-helper/vite-plugin-uni-components` 插件自动注册组件，支持深度扫描和类型声明。
- **页面和布局管理**: 使用 `@uni-helper/vite-plugin-uni-pages` 和 `@uni-helper/vite-plugin-uni-layouts` 插件来管理页面和布局。
- **平台支持**: 使用 `@uni-helper/vite-plugin-uni-platform` 插件来支持多平台开发。
- **Manifest 管理**: 使用 `@uni-helper/vite-plugin-uni-manifest` 插件来管理应用的 manifest 文件。
- **优化插件**: 使用 `@uni-ku/bundle-optimizer` 插件进行分包优化和异步模块加载。
- **开发工具**: 使用 `vite-plugin-restart` 插件在修改配置文件时自动重启开发服务器。

这些插件和库的组合使得项目在开发过程中更加高效，并且支持多平台的构建和发布。

## Store 用法

应用程序使用 `Pinia` 进行状态管理。以下是 store 的结构概述：

- **Store 初始化**：在 `src/store/index.ts` 中使用 `createPinia` 初始化 store，并包含一个持久化状态插件以实现数据持久化。

  ```typescript
  import { createPinia } from 'pinia'
  import { createPersistedState } from 'pinia-plugin-persistedstate'

  const store = createPinia()
  store.use(
    createPersistedState({
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    }),
  )

  export default store
  ```

- **用户 Store**：在 `src/store/user.ts` 中定义，管理用户信息、登录、注销和用户数据检索。

  ```typescript
  import { defineStore } from 'pinia'
  import { ref } from 'vue'

  export const useUserStore = defineStore('user', () => {
    const userInfo = ref({
      /* 初始状态 */
    })
    const setUserInfo = (val) => {
      /* 设置用户信息 */
    }
    const removeUserInfo = () => {
      /* 删除用户信息 */
    }
    const login = async (credentials) => {
      /* 登录逻辑 */
    }
    const getUserInfo = async () => {
      /* 获取用户信息 */
    }
    const logout = async () => {
      /* 注销逻辑 */
    }

    return { userInfo, login, getUserInfo, logout }
  })
  ```

## TabBar 用法

`fg-tabbar` 组件是一个自定义的标签栏实现，位于 `src/components/fg-tabbar/fg-tabbar.vue`。它提供了一个灵活的标签栏，支持不同的图标类型。

- **模板结构**：组件使用 `wd-tabbar` 和 `wd-tabbar-item` 来渲染标签项。支持的图标类型包括 `wot`、`unocss`、`iconfont` 和 `local`。

  ```vue
  <template>
    <wd-tabbar v-model="tabbarStore.curIdx" @change="selectTabBar">
      <block v-for="item in tabbarList" :key="item.path">
        <wd-tabbar-item :title="item.text" :icon="item.icon"></wd-tabbar-item>
      </block>
    </wd-tabbar>
  </template>
  ```

- **脚本逻辑**：组件通过 `selectTabBar` 函数管理标签选择和导航，更新当前索引并切换标签。

  ```typescript
  function selectTabBar({ value: index }) {
    const url = tabbarList[index].path
    tabbarStore.setCurIdx(index)
    uni.switchTab({ url })
  }
  ```

## 其他组件

- **导航栏**：`fg-navbar` 组件提供了一个可定制的导航栏。
- **布局**：项目包含各种布局组件，用于页面结构化。

## 快速开始

要设置项目，请按照以下步骤操作：

1. **安装依赖**：运行 `pnpm install` 安装所有必要的包。
2. **运行应用程序**：使用 `pnpm dev` 启动开发服务器。
3. **生产构建**：执行 `pnpm build` 创建生产就绪的构建。

## 许可证

本项目采用 MIT 许可证。
